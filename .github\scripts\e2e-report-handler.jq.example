def process_feature:
    {
        uri: .uri,
        feature_name: ((.uri // "" | tostring) | gsub("\\\\"; "/") | split("/") | last | sub(".feature$"; "")),
        scenarios: [
            .elements[] |
            if type == "object" and (.keyword == "Scenario" or .keyword == "Scenario Outline") then
                {
                    name: .name,
                    step_statuses: (try ([.steps[]? | .result? | .status? | select(. != null)] | unique) catch []),
                    is_hidden: (if has("hidden") then .hidden else false end)
                }
            else
                empty
            end
        ]
    };

def calculate_counts:
  {
    feature: .feature_name,
    total: (.scenarios | length),
    passed: (
      [.scenarios[] |
      select(
        (.step_statuses | length > 0) and
        (.step_statuses | all(. == "passed")) and
        (.is_hidden == false)
      )] | length
    ),
    failed: (
      [.scenarios[] |
      select(
        (.step_statuses | index("failed")) and
        (.is_hidden == false)
      )] | length
    ),
    skipped: (
      [.scenarios[] |
      select(
        (.step_statuses | all(. == "skipped")) and
        (.is_hidden == false)
      )] | length
    )
  };

[
    .[] | 
    process_feature |
    calculate_counts
] |

(
    ["Feature", "Total", "Passed", "Failed", "Skipped"] | @tsv
),
(
    .[] |
    [
        .feature,
        (if .total == 0 then "-" else (.total | tostring) end),
        (if .passed == 0 then "-" else (.passed | tostring) end),
        (if .failed == 0 then "-" else (.failed | tostring) end),
        (if .skipped == 0 then "-" else (.skipped | tostring) end)
    ] | @tsv
)
