import { Before, Given, Then } from '@badeball/cypress-cucumber-preprocessor';
import { landingPage } from '../../../pages/landingPage';

Before(() => {
  landingPage.loginLandingPage();
});

Given('The user is in Landing Page', () => {
  landingPage.verifyLandingPage();
});

Then(
  'The user verifies the search bar and more actions button are visible',
  () => {
    landingPage.verifySearchBarAndMoreActionsButton();
  }
);

Then('The user verifies the page title is "ILLUMINATE"', () => {
  landingPage.verifyPageTitle();
});

Then(
  'The user verifies "My Cases" is the selected folder in the breadcrumbs',
  () => {
    landingPage.verifySelectedFolderInBreadcrumbs();
  }
);
