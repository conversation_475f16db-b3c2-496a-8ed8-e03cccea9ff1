@echo off
setlocal enabledelayedexpansion

REM ============================================
REM Test Script for E2E Reporting System
REM Veritone Illuminate App
REM ============================================

echo.
echo ============================================
echo   TESTING E2E REPORTING SYSTEM
echo ============================================
echo.

REM Check if jq is available
where jq >nul 2>&1
if errorlevel 1 (
    echo ERROR: jq is not installed or not in PATH
    echo Please install jq from https://stedolan.github.io/jq/download/
    echo Or use chocolatey: choco install jq
    pause
    exit /b 1
)

echo jq is available. Version:
jq --version
echo.

REM Create reports directory if it doesn't exist
if not exist "cypress\reports" (
    echo Creating reports directory...
    mkdir cypress\reports
)

echo ============================================
echo   TEST 1: SUCCESS SCENARIO
echo ============================================
echo.

REM Test with successful report
echo Testing with mock successful report...
copy "cypress\reports\mock-success-report.json" "cypress\reports\cucumber-json.json" >nul

echo Running report parser...
call .github\scripts\parse-report.bat
set "SUCCESS_EXIT_CODE=!errorlevel!"

echo.
echo Success test completed with exit code: !SUCCESS_EXIT_CODE!
if exist "report_output.txt" (
    echo Report generated successfully!
    echo.
    echo --- SUCCESS REPORT PREVIEW ---
    type report_output.txt | findstr /n "."
    echo --- END PREVIEW ---
    echo.
    ren "report_output.txt" "success_report_output.txt"
) else (
    echo ERROR: No report file generated!
)

echo.
echo ============================================
echo   TEST 2: MIXED RESULTS SCENARIO
echo ============================================
echo.

REM Test with mixed results report
echo Testing with mock mixed results report...
copy "cypress\reports\mock-mixed-report.json" "cypress\reports\cucumber-json.json" >nul

echo Running report parser...
call .github\scripts\parse-report.bat
set "MIXED_EXIT_CODE=!errorlevel!"

echo.
echo Mixed results test completed with exit code: !MIXED_EXIT_CODE!
if exist "report_output.txt" (
    echo Report generated successfully!
    echo.
    echo --- MIXED RESULTS REPORT PREVIEW ---
    type report_output.txt | findstr /n "."
    echo --- END PREVIEW ---
    echo.
    ren "report_output.txt" "mixed_report_output.txt"
) else (
    echo ERROR: No report file generated!
)

echo.
echo ============================================
echo   TEST 3: MISSING REPORT SCENARIO
echo ============================================
echo.

REM Test with missing report
echo Testing with missing report file...
if exist "cypress\reports\cucumber-json.json" (
    del "cypress\reports\cucumber-json.json"
)

echo Running report parser...
call .github\scripts\parse-report.bat
set "MISSING_EXIT_CODE=!errorlevel!"

echo.
echo Missing report test completed with exit code: !MISSING_EXIT_CODE!

echo.
echo ============================================
echo   TEST 4: JQ SCRIPT VALIDATION
echo ============================================
echo.

REM Test JQ script directly
echo Testing JQ script with success data...
jq -r -f .github\scripts\e2e-report-handler.jq "cypress\reports\mock-success-report.json"
set "JQ_SUCCESS_EXIT=!errorlevel!"

echo.
echo Testing JQ script with mixed data...
jq -r -f .github\scripts\e2e-report-handler.jq "cypress\reports\mock-mixed-report.json"
set "JQ_MIXED_EXIT=!errorlevel!"

echo.
echo ============================================
echo   TEST SUMMARY
echo ============================================
echo.

echo Test Results:
echo   1. Success Scenario:     !SUCCESS_EXIT_CODE! (0=pass, 1=fail)
echo   2. Mixed Results:        !MIXED_EXIT_CODE! (1=expected for failures)
echo   3. Missing Report:       !MISSING_EXIT_CODE! (1=expected for missing)
echo   4. JQ Success Script:    !JQ_SUCCESS_EXIT! (0=pass)
echo   5. JQ Mixed Script:      !JQ_MIXED_EXIT! (0=pass)

echo.
echo Generated Files:
if exist "success_report_output.txt" (
    echo   ✅ success_report_output.txt
) else (
    echo   ❌ success_report_output.txt (missing)
)

if exist "mixed_report_output.txt" (
    echo   ✅ mixed_report_output.txt
) else (
    echo   ❌ mixed_report_output.txt (missing)
)

echo.
echo Mock Data Files:
echo   📄 cypress\reports\mock-success-report.json
echo   📄 cypress\reports\mock-mixed-report.json

echo.
echo Script Files:
echo   📄 .github\scripts\e2e-report-handler.jq
echo   📄 .github\scripts\parse-report.bat
echo   📄 .github\scripts\parse-report.sh
echo   📄 .github\workflows\daily_e2e.yml

echo.
REM Validate expected results
set "OVERALL_SUCCESS=true"

if !SUCCESS_EXIT_CODE! neq 0 (
    echo ❌ SUCCESS test should have exit code 0
    set "OVERALL_SUCCESS=false"
)

if !MIXED_EXIT_CODE! neq 1 (
    echo ❌ MIXED test should have exit code 1 (failures detected)
    set "OVERALL_SUCCESS=false"
)

if !MISSING_EXIT_CODE! neq 1 (
    echo ❌ MISSING test should have exit code 1 (no report found)
    set "OVERALL_SUCCESS=false"
)

if !JQ_SUCCESS_EXIT! neq 0 (
    echo ❌ JQ SUCCESS script should have exit code 0
    set "OVERALL_SUCCESS=false"
)

if !JQ_MIXED_EXIT! neq 0 (
    echo ❌ JQ MIXED script should have exit code 0
    set "OVERALL_SUCCESS=false"
)

echo.
if "%OVERALL_SUCCESS%"=="true" (
    echo 🎉 ALL TESTS PASSED! The reporting system is working correctly.
    echo.
    echo Next Steps:
    echo   1. Configure your cypress.env.json with real test credentials
    echo   2. Run 'run-e2e-tests.bat' to execute actual Cypress tests
    echo   3. Set up GitHub secrets for the daily workflow:
    echo      - CYPRESS_USER1_USERNAME
    echo      - CYPRESS_USER1_PASSWORD  
    echo      - CYPRESS_USER2_USERNAME
    echo      - CYPRESS_USER2_PASSWORD
    echo      - SLACK_WEBHOOK (optional)
    echo      - SLACK_CHANNEL (optional)
    exit /b 0
) else (
    echo ❌ SOME TESTS FAILED! Please check the errors above.
    echo.
    pause
    exit /b 1
)
