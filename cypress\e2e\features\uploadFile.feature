Feature: Upload

  Background: Delete Folder
    Given Navigate to upload folder and delete file:
      | filename                                   |
      | e2e/upload/image-plant-jpeg.jpeg           |
      | e2e/upload/spanish-pdf.pdf                 |
      | e2e/upload/spanish-email.eml               |
      | e2e/upload/spanish-txt.txt                 |
      | e2e/upload/edited-media-file-before-upload |
      | e2e/upload/image-plant-jpeg.jpeg           |
      | e2e/upload/edited-test-file                |
      | e2e/upload/test-multiple-files             |
      | e2e/upload/200.jpg                         |
      | e2e/upload/e2e_audio.mp3                   |

  @e2e @upload
  Scenario: Verify user can upload multiple files
    When The user uploads multiple files for processing

  @e2e @upload
  Scenario: The user verifies the initial state of the upload screen
    When The user opens the upload screen
    Then The screen is displayed correctly with all default elements

  @e2e @upload
  Scenario: Verify user can upload by browse
    When The user uploads a file by browse

  @e2e @upload @skip
  Scenario: Verify user can upload by drag and drop
  # TODO: Implement proper drag and drop upload test
  #   When The user uploads a file by drag and drop

  @e2e @upload @uploadTranscription
  Scenario: Upload video file for transcription
    When Upload video file for transcription

  @e2e @upload @uploadTranscription
  Scenario: Upload audio for transcription
    When Upload audio for transcription

  @e2e @upload @uploadImage
  Scenario: Upload image file for object detection
    When The user upload image file for object detection

  @e2e @upload @uploadTextExtraction
  Scenario: Upload PDF file for text extraction
    When Upload pdf file for text extraction

  @e2e @upload @uploadTextExtraction
  Scenario: Upload EML file for text extraction
    When Upload eml file for text extraction

  @e2e @uploadTranslation
  Scenario: Upload text file for spanish to english translation
    When Upload text file for spanish to english translation

  @e2e @upload
  Scenario: Verify user can upload image by URL
    When The user uploads an image by URL

  @e2e @upload
  Scenario: Verify user can add more file in upload screen by use + icon
    When The user adds more files using the plus icon

  @e2e @upload
  Scenario: Verify user can edit a selected file in Upload screen
    When The user edits a selected file in upload screen

  @e2e @upload
  Scenario: Verify user can delete selected file in Upload screen
    When The user deletes a selected file in upload screen

  @e2e @upload @uploadWithoutEngines
  Scenario: Verify user can upload file without running any engines
    When The user uploads a file without running any engines

  @e2e @upload @translateWithTranscription
  Scenario: Verify Translate can only run with audio/video file run Transcription
    When The user uploads audio file with translation that requires transcription

  @e2e @upload
  Scenario: Verify user can edit media before upload
    When The user edits media file before completing upload

  @e2e @upload
  Scenario: Verify user can add tags in edit media before upload
    When The user adds tags in edit media before upload

  @e2e @upload
  Scenario: Verify user can close the Upload screen
    When The user closes the upload screen

  @e2e @upload @skip
  Scenario: Verify that when a user uploads a media file without faces and then run Facial Detection engine
  # TODO:
  # When The user uploads media file without faces and runs facial detection
  # REASON: Facial Detection engine is not available in the test environment

  @e2e @upload @transcriptionNoAudio
  Scenario: Verify that when a user uploads a media file without audio and then run Transcription engine
    When The user uploads media file without audio and runs transcription
    Then The console should not log any errors
