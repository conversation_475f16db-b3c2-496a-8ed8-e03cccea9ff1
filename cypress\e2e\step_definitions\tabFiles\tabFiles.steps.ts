import { When, Before } from '@badeball/cypress-cucumber-preprocessor';
import { landingPage } from '../../../pages/landingPage';

Before(() => {
  landingPage.loginLandingPage();
  cy.NavigateToTestFolder();
  // Replace fixed wait with a conditional wait for UI readiness.
  // This ensures the page (specifically the files table rows) is loaded
  // before proceeding.
  cy.contains('FILES').click();
  cy.get('[data-testid^=files-table-row]')
    .should('be.visible')
    .and('have.length.gt', 0);
});

When(
  'The user verifies existing data for file name, TDO, Created date, file type',
  () => {
    landingPage.verifyExistingDataForFileName();
  }
);
