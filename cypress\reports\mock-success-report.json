[{"description": "", "elements": [{"description": "", "id": "landing-page-functionality;user-can-access-landing-page", "keyword": "<PERSON><PERSON><PERSON>", "line": 4, "name": "User can access landing page", "steps": [{"arguments": [], "keyword": "Given ", "line": 5, "name": "I navigate to the application", "result": {"duration": 2500000000, "status": "passed"}}, {"arguments": [], "keyword": "Then ", "line": 6, "name": "I should see the landing page", "result": {"duration": 1000000000, "status": "passed"}}], "tags": [], "type": "scenario"}, {"description": "", "id": "landing-page-functionality;user-can-login-successfully", "keyword": "<PERSON><PERSON><PERSON>", "line": 8, "name": "User can login successfully", "steps": [{"arguments": [], "keyword": "Given ", "line": 9, "name": "I am on the login page", "result": {"duration": 1500000000, "status": "passed"}}, {"arguments": [], "keyword": "When ", "line": 10, "name": "I enter valid credentials", "result": {"duration": 2000000000, "status": "passed"}}, {"arguments": [], "keyword": "Then ", "line": 11, "name": "I should be logged in", "result": {"duration": 3000000000, "status": "passed"}}], "tags": [], "type": "scenario"}], "id": "landing-page-functionality", "keyword": "Feature", "line": 1, "name": "Landing Page Functionality", "tags": [], "uri": "cypress/e2e/features/landingPage.feature"}, {"description": "", "elements": [{"description": "", "id": "search-functionality;user-can-perform-basic-search", "keyword": "<PERSON><PERSON><PERSON>", "line": 4, "name": "User can perform basic search", "steps": [{"arguments": [], "keyword": "Given ", "line": 5, "name": "I am logged in", "result": {"duration": 2000000000, "status": "passed"}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "I enter a search term", "result": {"duration": 1500000000, "status": "passed"}}, {"arguments": [], "keyword": "Then ", "line": 7, "name": "I should see search results", "result": {"duration": 2500000000, "status": "passed"}}], "tags": [], "type": "scenario"}, {"description": "", "id": "search-functionality;user-can-filter-search-results", "keyword": "<PERSON><PERSON><PERSON>", "line": 9, "name": "User can filter search results", "steps": [{"arguments": [], "keyword": "Given ", "line": 10, "name": "I have search results displayed", "result": {"duration": 1000000000, "status": "passed"}}, {"arguments": [], "keyword": "When ", "line": 11, "name": "I apply a filter", "result": {"duration": 1800000000, "status": "passed"}}, {"arguments": [], "keyword": "Then ", "line": 12, "name": "I should see filtered results", "result": {"duration": 2200000000, "status": "passed"}}], "tags": [], "type": "scenario"}], "id": "search-functionality", "keyword": "Feature", "line": 1, "name": "Search Functionality", "tags": [], "uri": "cypress/e2e/features/search.feature"}, {"description": "", "elements": [{"description": "", "id": "file-upload-functionality;user-can-upload-media-file", "keyword": "<PERSON><PERSON><PERSON>", "line": 4, "name": "User can upload media file", "steps": [{"arguments": [], "keyword": "Given ", "line": 5, "name": "I am on the upload page", "result": {"duration": 1500000000, "status": "passed"}}, {"arguments": [], "keyword": "When ", "line": 6, "name": "I select a media file", "result": {"duration": 2000000000, "status": "passed"}}, {"arguments": [], "keyword": "And ", "line": 7, "name": "I click upload", "result": {"duration": 5000000000, "status": "passed"}}, {"arguments": [], "keyword": "Then ", "line": 8, "name": "the file should be uploaded successfully", "result": {"duration": 3000000000, "status": "passed"}}], "tags": [], "type": "scenario"}], "id": "file-upload-functionality", "keyword": "Feature", "line": 1, "name": "File Upload Functionality", "tags": [], "uri": "cypress/e2e/features/uploadFile.feature"}]