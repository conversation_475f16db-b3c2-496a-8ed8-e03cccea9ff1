import {
  categoryIdTranslate,
  engineIdTranslateSpanishToEnglish,
  engineIdAmazonTranslate,
} from '../fixtures/variables';

export const landingPage = {
  loginLandingPage: (): void => {
    cy.LoginLandingPage();
  },
  verifyLandingPage: (): void => {
    navItems.forEach((item) => cy.contains(item).click());
    cy.get('button[aria-label="close upload"]').click();
    cy.contains('FILTERS').click();
    cy.get('button[aria-label="close filters"]').click();
    cy.contains('ANALYTICS').click();
  },
  verifySearchBarAndMoreActionsButton: (): void => {
    cy.get('[data-test="main-entity-search"]').should('be.visible');
    cy.get('[data-test="main-entity-search"]').contains('Search');
    cy.get('[data-veritone-element="more_actions"]').should('be.visible');
  },
  verifyPageTitle: (): void => {
    cy.contains('ILLUMINATE');
  },
  verifySelectedFolderInBreadcrumbs: (): void => {
    cy.get('[data-test="top-bar-breadcrumbs"]')
      .find('button')
      .should('have.length', 2)
      .eq(1)
      .contains('My Cases');
  },
  createFolder: (folderName: string, parentFolderName: string): void => {
    cy.get('[data-test="top-bar-select-folder"]').click();

    if (parentFolderName === 'My Cases') {
      cy.get('[data-test="root-folder"]').click();
    } else {
      cy.contains(parentFolderName).click();
    }

    cy.get('[data-test="folder-modal-create-new-folder-button"]').click();
    cy.contains('Create Folder');
    cy.contains(`Create folder in ${parentFolderName}`);
    cy.get('[data-test="create-folder-enter-folder-name"] input').clear();
    cy.get('[data-test="create-folder-enter-folder-name"] input').type(
      folderName
    );
    cy.get('[data-test="create-folder-submit-button"]').click();
    cy.contains(`New Folder: ${folderName} has been created`).should(
      'be.visible'
    );

    if (parentFolderName !== 'My Cases') {
      cy.contains(folderName);
    }

    cy.get('[aria-label="Close Folder"]').click();
  },
  createFolderInMyCases: (
    folderName: string,
    parentFolderName: string
  ): void => {
    cy.get('[data-test="top-bar-select-folder"]').click();
    cy.get('[data-test="root-folder"]').click();
    cy.get('[data-test="folder-modal-create-new-folder-button"]').click();
    cy.contains('Create Folder');
    cy.contains(`Create folder in ${parentFolderName}`);
    cy.get('[data-test="create-folder-enter-folder-name"] input').clear();
    cy.get('[data-test="create-folder-enter-folder-name"] input').type(
      folderName
    );
    cy.get('[data-test="create-folder-submit-button"]').click();
    cy.contains(`New Folder: ${folderName} has been created`).should(
      'be.visible'
    );
    cy.get('[aria-label="Close Folder"]').click();
  },
  renameFolder: (
    folderName: string,
    renamedFolder: string,
    parentFolderName: string
  ): void => {
    cy.get('[data-test="top-bar-select-folder"]').click();

    if (parentFolderName === 'My Cases') {
      cy.get('[data-test="root-folder"]').click();
    } else {
      cy.contains(parentFolderName).click();
    }

    cy.contains(folderName).next().click({ force: true });
    cy.get('[role="menuitem"]').contains('Rename').click({ force: true });
    cy.get('[data-test="create-folder-enter-folder-name"] input').clear();
    cy.get('[data-test="create-folder-enter-folder-name"] > input').type(
      renamedFolder
    );
    cy.get('[data-test="create-folder-submit-button"]').click();

    if (parentFolderName === 'My Cases') {
      cy.contains(renamedFolder).should('be.visible');
    } else {
      cy.get('[aria-label="Close Folder"]').click();
      cy.get('[data-test="top-bar-select-folder"]').click();
      cy.contains(parentFolderName).click();
      cy.contains(renamedFolder);
    }

    cy.get('[aria-label="Close Folder"]').click();
  },
  renameFolderInMyCases: (folderName: string, renamedFolder: string): void => {
    cy.get('[data-test="top-bar-select-folder"]').click();
    cy.get('[data-test="root-folder"]').click();
    cy.contains(folderName).next().click({ force: true });
    cy.get('[role="menuitem"]').contains('Rename').click({ force: true });
    cy.get('[data-test="create-folder-enter-folder-name"] input').clear();
    cy.get('[data-test="create-folder-enter-folder-name"] > input').type(
      renamedFolder
    );
    cy.get('[data-test="create-folder-submit-button"]').click();
    cy.contains(renamedFolder).should('be.visible');
    cy.get('[aria-label="Close Folder"]').click();
  },
  moveFolder: (
    folderToMoveName: string,
    sourceParentName: string,
    destinationParentName: string
  ): void => {
    cy.get('[data-test="top-bar-select-folder"]').click();

    if (destinationParentName === 'My Cases') {
      cy.get('[data-test="folder-modal-dialog-content"]')
        .contains(sourceParentName)
        .click();
      cy.contains('[data-testid="list-item"]', folderToMoveName)
        .find('[data-testid="icon-button"][aria-label="menu-button"]')
        .click({ force: true });
    } else {
      cy.contains(sourceParentName).click();
      cy.contains(folderToMoveName).next().click({ force: true });
    }

    cy.get('[role="menuitem"]').contains('Move').click();
    cy.get('[data-test="move-folder-dialog-content"]')
      .contains(destinationParentName)
      .click({ force: true });
    cy.get('[data-test="move-folder-submit-button"]').click();
    cy.contains(`Folder ${folderToMoveName} has been moved`).should(
      'be.visible'
    );
    cy.get('[aria-label="Close Folder"]').click();

    cy.get('[data-test="top-bar-select-folder"]').click();
    if (destinationParentName === 'My Cases') {
      cy.get('[data-test="folder-modal-dialog-content"]')
        .contains(sourceParentName)
        .click();
      cy.get('[data-test="folder-modal-dialog-content"]')
        .find(
          `[data-testid="list-item-text"][aria-label="${folderToMoveName}"]`
        )
        .should('have.length', 1);
    } else {
      cy.contains(sourceParentName).click();
      cy.contains(folderToMoveName).should('not.exist');
      cy.contains(destinationParentName).click();
      cy.contains(folderToMoveName);
    }
    cy.get('[aria-label="Close Folder"]').click();
  },
  moveFolderToMyCases: (
    folderToMoveName: string,
    sourceParentName: string,
    destinationParentName: string
  ): void => {
    cy.get('[data-test="top-bar-select-folder"]').click();
    cy.get('[data-test="folder-modal-dialog-content"]')
      .contains(sourceParentName)
      .click();
    cy.contains('[data-testid="list-item"]', folderToMoveName)
      .find('[data-testid="icon-button"][aria-label="menu-button"]')
      .click({ force: true });
    cy.get('[role="menuitem"]').contains('Move').click();
    cy.get('[data-test="move-folder-dialog-content"]')
      .contains(destinationParentName)
      .click({ force: true });
    cy.get('[data-test="move-folder-submit-button"]').click();
    cy.contains(`Folder ${folderToMoveName} has been moved`).should(
      'be.visible'
    );
    cy.get('[aria-label="Close Folder"]').click();
    cy.get('[data-test="top-bar-select-folder"]').click();
    cy.get('[data-test="folder-modal-dialog-content"]')
      .contains(sourceParentName)
      .click();
    cy.get('[data-test="folder-modal-dialog-content"]')
      .find(`[data-testid="list-item-text"][aria-label="${folderToMoveName}"]`)
      .should('have.length', 1);
    cy.get('[aria-label="Close Folder"]').click();
  },
  reprocessesMP3FileForSpanishToEnglishTranslation: (
    fileName: string
  ): void => {
    cy.NavigateToReprocessFolder('reprocess');
    cy.ReprocessBasic(
      categoryIdTranslate,
      engineIdTranslateSpanishToEnglish,
      fileName
    );
  },
  reprocessesEMLFileForSpanishToEnglishTranslation: (
    fileName: string
  ): void => {
    cy.NavigateToReprocessFolder('upload');
    cy.ReprocessBasic(
      categoryIdTranslate,
      engineIdTranslateSpanishToEnglish,
      fileName
    );
  },
  reprocessesPDFFileForSpanishToEnglishTranslation: (
    fileName: string
  ): void => {
    cy.NavigateToReprocessFolder('upload');
    cy.ReprocessBasic(
      categoryIdTranslate,
      engineIdTranslateSpanishToEnglish,
      fileName
    );
  },
  reprocessesVideoFileForAmazonTranslate: (): void => {
    cy.ReprocessFileVideoWithEngineAmazonTranslate(
      categoryIdTranslate,
      engineIdAmazonTranslate
    );
  },
  verifyTotalNumberOfFiles: (num: number): void => {
    cy.contains('TOTAL NUMBER OF FILES');
    cy.get('[data-test="TOTAL NUMBER OF FILES"]').contains(num);
  },
  verifyTotalMediaHours: (num: number): void => {
    cy.contains('TOTAL MEDIA HOURS');
    cy.get('[data-test="TOTAL MEDIA HOURS"]').contains(num);
  },
  verifyTotalMediaHoursProcessed: (num: number): void => {
    cy.contains('TOTAL MEDIA HOURS PROCESSED');
    cy.get('[data-test="TOTAL MEDIA HOURS PROCESSED"]').contains(num);
  },
  verifyTranscriptionNumber: (num: number): void => {
    cy.get('[data-test="dashboard-card/render-card/Transcription"]').contains(
      num
    );
  },
  verifyTranslateNumber: (num: number): void => {
    cy.get('[data-test="dashboard-card/render-card/Translate"]').contains(num);
  },
  verifyFacialDetectionNumber: (num: number): void => {
    cy.get(
      '[data-test="dashboard-card/render-card/Facial Detection"]'
    ).contains(num);
  },
  verifyObjectDetectionNumber: (num: number): void => {
    cy.get(
      '[data-test="dashboard-card/render-card/Object Detection"]'
    ).contains(num);
  },
  verifyEntityExtractionNumber: (num: number): void => {
    cy.get(
      '[data-test="dashboard-card/render-card/Entity Extraction"]'
    ).contains(num);
  },
  verifyContentClassificationNumber: (num: number): void => {
    cy.get(
      '[data-test="dashboard-card/render-card/Content Classification"]'
    ).contains(num);
  },
  verifyTextRecognitionNumber: (num: number): void => {
    cy.get(
      '[data-test="dashboard-card/render-card/Text Recognition"]'
    ).contains(num);
  },
  verifySpeakerDetectionNumber: (num: number): void => {
    cy.get(
      '[data-test="dashboard-card/render-card/Speaker Detection"]'
    ).contains(num);
  },
  verifyWordCloudIsDisplayed: (): void => {
    cy.contains('Word Cloud');
    cy.contains('economy');
  },
  verifyEntityClustersIsDisplayed: (): void => {
    cy.contains('Entity Clusters');
    cy.get('.rv-xy-plot').should('be.visible');
  },
  VerifyExportInformationOnHistoryTab: (): void => {
    cy.contains('EXPORTS').click();
    cy.get('[data-testid=export-table-row]').each(($row) => {
      cy.wrap($row).within(() => {
        cy.root()
          .children()
          .eq(1)
          .invoke('text')
          .invoke('trim')
          .should('not.be.empty');

        cy.root()
          .children()
          .eq(2)
          .invoke('text')
          .invoke('trim')
          .should('not.be.empty');

        cy.root()
          .children()
          .eq(3)
          .invoke('text')
          .invoke('trim')
          .should('not.be.empty');
      });
    });
  },
  verifyExistingDataForFileName: (): void => {
    cy.get('[data-testid^=files-table-row]').each(($row) => {
      cy.wrap($row).within(() => {
        cy.root()
          .children()
          .eq(1)
          .invoke('text')
          .invoke('trim')
          .should('not.be.empty');

        cy.root()
          .children()
          .eq(3)
          .invoke('text')
          .invoke('trim')
          .should('not.be.empty');

        cy.root()
          .children()
          .eq(4)
          .invoke('text')
          .invoke('trim')
          .should('not.be.empty');

        cy.root()
          .children()
          .eq(5)
          .invoke('text')
          .invoke('trim')
          .should('not.be.empty');
      });
    });
  },
  checkProcessingStatus: (): void => {
    cy.get('[data-test="refresh-processing-status"]').should('be.visible');
    cy.get('[data-test="refresh-processing-status"]').click();
  },

  clickFolderIcon: (): void => {
    cy.get('[data-test="top-bar-select-folder"]').click();
  },
  getFolderDialog: () => {
    return cy.get('[data-test="folder-modal-dialog-content"]');
  },
  getRootFolder: () => {
    return cy.get('[data-test="root-folder"]');
  },

  closeFolderDialog: (buttonType: 'X' | 'Cancel'): void => {
    if (buttonType === 'X') {
      cy.get('button[aria-label="Close Folder"]').click();
    } else {
      cy.get('[data-test="folder-modal-cancel-folder-button"]').click();
    }
  },
  selectFolder: (folderName: string): void => {
    cy.get('[data-test="folder-modal-dialog-content"]')
      .contains('[data-testid="list-item"]', folderName)
      .click();
    cy.get('[data-test="folder-modal-select-folder-button"]').click();
  },
  getBreadcrumb: (folderName: string) => {
    return cy
      .get('[data-test="top-bar-breadcrumbs"]')
      .contains('button', folderName);
  },
  hoverOverFolder: (folderName: string): void => {
    if (folderName === 'MY CASES') {
      cy.get('[data-test="root-folder"]').trigger('mouseover');
    } else {
      cy.get('[data-test="folder-modal-dialog-content"]')
        .contains('[data-testid="list-item"]', folderName)
        .trigger('mouseover');
    }
  },

  getTooltip: () => {
    return cy.get('[role="tooltip"]');
  },
};

const navItems = [
  'My Cases',
  'FILES',
  'EXPORTS',
  'PROCESSING STATUS',
  'UPLOAD',
];
