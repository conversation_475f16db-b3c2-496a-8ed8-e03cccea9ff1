import { Before, When } from '@badeball/cypress-cucumber-preprocessor';
import { landingPage } from '../../../pages/landingPage';

Before(() => {
  cy.LoginLandingPage();
  cy.NavigateToTestFolder();
  // sometime, it fails at the initial access. wait for 2 seconds seems help.
  // eslint-disable-next-line cypress/no-unnecessary-waiting
  cy.wait(2000);
});

When('The total number of files is {int}', (num: number) => {
  landingPage.verifyTotalNumberOfFiles(num);
});

When('The total number of media hours is {int}', (num: number) => {
  landingPage.verifyTotalMediaHours(num);
});

When('The total number of media hours processed is {int}', (num: number) => {
  landingPage.verifyTotalMediaHoursProcessed(num);
});

When('The transcription number is {int}', (num: number) => {
  landingPage.verifyTranscriptionNumber(num);
});

When('The translate number is {int}', (num: number) => {
  landingPage.verifyTranslateNumber(num);
});

When('The facial detection number is {int}', (num: number) => {
  landingPage.verifyFacialDetectionNumber(num);
});

When('The object detection number is {int}', (num: number) => {
  landingPage.verifyObjectDetectionNumber(num);
});

When('The entity extraction number is {int}', (num: number) => {
  landingPage.verifyEntityExtractionNumber(num);
});

When('The content classification number is {int}', (num: number) => {
  landingPage.verifyContentClassificationNumber(num);
});

When('The text recognition number is {int}', (num: number) => {
  landingPage.verifyTextRecognitionNumber(num);
});

When('The speaker detection number is {int}', (num: number) => {
  landingPage.verifySpeakerDetectionNumber(num);
});

When('The word cloud is displayed', () => {
  landingPage.verifyWordCloudIsDisplayed();
});

When('The entity clusters is displayed', () => {
  landingPage.verifyEntityClustersIsDisplayed();
});
