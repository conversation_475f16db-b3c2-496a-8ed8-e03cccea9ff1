# Daily E2E Test Reporting System
## Veritone Illuminate App

This document describes the daily E2E test reporting system for the Veritone Illuminate App, including local Windows testing capabilities.

## 📋 Overview

The system provides:
- **Daily automated E2E testing** via GitHub Actions
- **Comprehensive test reporting** with Slack notifications
- **Local Windows testing environment** with batch scripts
- **Mock data testing** for validation

## 🏗️ System Architecture

### Components

1. **GitHub Actions Workflow** (`.github/workflows/daily_e2e.yml`)
   - Runs daily at midnight UTC
   - Sets up SSL certificates and application server
   - Executes Cypress tests
   - Generates and sends reports

2. **Report Processing Scripts**
   - `.github/scripts/e2e-report-handler.jq` - JSON processing with jq
   - `.github/scripts/parse-report.sh` - Linux/GitHub Actions report parser
   - `.github/scripts/parse-report.bat` - Windows report parser

3. **Local Testing Scripts**
   - `run-e2e-tests.bat` - Complete local E2E test execution
   - `test-reporting-system.bat` - Validate reporting with mock data

4. **Mock Test Data**
   - `cypress/reports/mock-success-report.json` - All tests passing
   - `cypress/reports/mock-mixed-report.json` - Mixed results with failures

## 🚀 Quick Start

### Prerequisites

**For Local Windows Testing:**
- Node.js 20.x or higher
- Yarn package manager
- jq (for report parsing)
  ```bash
  # Install via Chocolatey
  choco install jq
  
  # Or download from https://stedolan.github.io/jq/download/
  ```

**For GitHub Actions:**
- Repository secrets configured (see Configuration section)

### Local Testing

1. **Test the reporting system first:**
   ```cmd
   test-reporting-system.bat
   ```

2. **Run full E2E tests:**
   ```cmd
   run-e2e-tests.bat
   ```

3. **Configure test credentials:**
   Edit `cypress.env.json` with your test user credentials:
   ```json
   {
     "username": "your-test-username",
     "password": "your-test-password",
     "users": {
       "user1": {
         "username": "user1-username",
         "password": "user1-password"
       },
       "user2": {
         "username": "user2-username", 
         "password": "user2-password"
       }
     }
   }
   ```

## ⚙️ Configuration

### GitHub Repository Secrets

Configure these secrets in your GitHub repository:

**Required:**
- `CYPRESS_USER1_USERNAME` - Primary test user username
- `CYPRESS_USER1_PASSWORD` - Primary test user password
- `CYPRESS_USER2_USERNAME` - Secondary test user username  
- `CYPRESS_USER2_PASSWORD` - Secondary test user password

**Optional (for Slack notifications):**
- `SLACK_WEBHOOK` - Slack webhook URL for notifications
- `SLACK_CHANNEL` - Slack channel name (defaults to 'general')

### Workflow Schedule

The daily workflow runs at:
- **UTC:** 00:00 (midnight)
- **EST:** 19:00 (7 PM)
- **EDT:** 20:00 (8 PM)

To modify the schedule, edit the cron expression in `.github/workflows/daily_e2e.yml`:
```yaml
schedule:
  - cron: "0 0 * * *"  # Modify this line
```

## 📊 Report Format

### Test Summary Table
```
+------------------+-------+--------+--------+---------+
| Feature          | Total | Passed | Failed | Skipped |
+------------------+-------+--------+--------+---------+
| landingPage      | 2     | 1      | 1      | -       |
| search           | 2     | 1      | 1      | -       |
| uploadFile       | 2     | 1      | 1      | -       |
| bookmark         | 1     | -      | -      | 1       |
| Total            | 7     | 3      | 3      | 1       |
+------------------+-------+--------+--------+---------+
```

### Failed Scenarios Details
When failures occur (≤3 scenarios), detailed information is included:
```
- Feature: `landingPage`
  Scenario: `User login fails with invalid credentials`
- Feature: `search`  
  Scenario: `User can perform basic search`
```

## 🔧 Troubleshooting

### Common Issues

**1. Application won't start locally**
- Check if port 8080 is already in use
- Verify SSL certificates are properly configured
- Check `app_startup.log` for errors

**2. jq not found error**
- Install jq: `choco install jq` (Windows with Chocolatey)
- Or download from: https://stedolan.github.io/jq/download/

**3. Cypress tests fail to run**
- Ensure `cypress.env.json` has valid credentials
- Check if application is accessible at `https://local.veritone.com:8080`
- Verify Chrome browser is installed

**4. GitHub Actions workflow fails**
- Check repository secrets are configured
- Verify the application builds successfully
- Review workflow logs for specific errors

### Log Files

**Local Testing:**
- `app_startup.log` - Application server startup logs
- `cypress/reports/cucumber-json.json` - Raw test results
- `report_output.txt` - Formatted test report
- `cypress/screenshots/` - Failure screenshots
- `cypress/videos/` - Test execution videos

**GitHub Actions:**
- Workflow logs available in Actions tab
- Artifacts uploaded for screenshots, videos, and reports

## 🧪 Testing the System

### Validate Reporting System
```cmd
# Test with mock data
test-reporting-system.bat

# This will test:
# ✅ Success scenario (all tests pass)
# ✅ Mixed results (some failures/skips)  
# ✅ Missing report handling
# ✅ JQ script functionality
```

### Manual Testing
```cmd
# Run actual E2E tests
run-e2e-tests.bat

# Or run specific Cypress commands
yarn cy:run --browser chrome
yarn cy:open  # Interactive mode
```

## 📁 File Structure

```
├── .github/
│   ├── scripts/
│   │   ├── e2e-report-handler.jq      # JSON processing script
│   │   ├── parse-report.sh            # Linux report parser
│   │   └── parse-report.bat           # Windows report parser
│   └── workflows/
│       └── daily_e2e.yml              # GitHub Actions workflow
├── cypress/
│   ├── reports/
│   │   ├── mock-success-report.json   # Mock successful tests
│   │   └── mock-mixed-report.json     # Mock mixed results
│   └── e2e/                           # Existing Cypress tests
├── run-e2e-tests.bat                  # Local E2E test runner
├── test-reporting-system.bat          # Reporting system validator
└── E2E-TESTING-README.md             # This documentation
```

## 🔄 Workflow Process

1. **Trigger:** Daily schedule or manual dispatch
2. **Setup:** Install dependencies, configure SSL, start application
3. **Test:** Run Cypress E2E tests with Chrome browser
4. **Report:** Parse JSON results, format summary table
5. **Notify:** Send Slack notification with results
6. **Cleanup:** Stop application, upload artifacts

## 📈 Monitoring & Maintenance

### Regular Tasks
- Monitor daily workflow execution
- Review test failure patterns
- Update test credentials as needed
- Maintain Slack webhook configuration

### Scaling Considerations
- Add more test scenarios as application grows
- Consider parallel test execution for larger test suites
- Implement test result trending and analytics
- Add email notifications as backup to Slack

## 🤝 Contributing

When adding new E2E tests:
1. Follow existing Cucumber/Gherkin patterns
2. Add appropriate step definitions
3. Test locally before committing
4. Ensure tests work in headless Chrome mode

For reporting system changes:
1. Test with mock data first
2. Validate both Windows and Linux compatibility
3. Update documentation as needed
