@echo off
setlocal enabledelayedexpansion

REM ============================================
REM Veritone Illuminate App - Local E2E Test Runner
REM Windows Batch Script
REM ============================================

echo.
echo ============================================
echo   VERITONE ILLUMINATE APP - E2E TEST RUNNER
echo ============================================
echo.

REM Check if Node.js is installed
where node >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if Yarn is installed
where yarn >nul 2>&1
if errorlevel 1 (
    echo ERROR: Yarn is not installed or not in PATH
    echo Please install Yarn from https://yarnpkg.com/
    pause
    exit /b 1
)

REM Check if jq is installed (needed for report parsing)
where jq >nul 2>&1
if errorlevel 1 (
    echo WARNING: jq is not installed. Report parsing will be skipped.
    echo To install jq: choco install jq (if you have Chocolatey)
    echo Or download from: https://stedolan.github.io/jq/download/
    set "JQ_AVAILABLE=false"
) else (
    set "JQ_AVAILABLE=true"
)

echo Node.js version:
node --version
echo.
echo Yarn version:
yarn --version
echo.

REM Create reports directory if it doesn't exist
if not exist "cypress\reports" (
    echo Creating reports directory...
    mkdir cypress\reports
)

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo Installing dependencies...
    yarn install
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
) else (
    echo Dependencies already installed.
)

REM Check if cypress.env.json exists
if not exist "cypress.env.json" (
    echo.
    echo WARNING: cypress.env.json not found!
    echo Creating a template file...
    (
        echo {
        echo   "username": "your-username",
        echo   "password": "your-password",
        echo   "users": {
        echo     "user1": {
        echo       "username": "your-username",
        echo       "password": "your-password"
        echo     },
        echo     "user2": {
        echo       "username": "another-username",
        echo       "password": "another-password"
        echo     }
        echo   }
        echo }
    ) > cypress.env.json
    echo.
    echo Please edit cypress.env.json with your actual test credentials.
    echo Press any key to continue or Ctrl+C to exit and configure credentials first.
    pause
)

echo.
echo ============================================
echo   STARTING APPLICATION SERVER
echo ============================================
echo.

REM Check if the application is already running
netstat -an | findstr ":8080" >nul 2>&1
if not errorlevel 1 (
    echo Application appears to be already running on port 8080.
    echo Please stop any existing instances or use a different port.
    echo.
    choice /c YN /m "Continue anyway (Y/N)?"
    if errorlevel 2 exit /b 1
)

REM Start the application server in the background
echo Starting application server...
start /b cmd /c "yarn startssl > app_startup.log 2>&1"

REM Wait for the application to start
echo Waiting for application to start...
timeout /t 15 /nobreak >nul

REM Check if the application is responding
echo Checking if application is ready...
powershell -Command "try { Invoke-WebRequest -Uri 'https://local.veritone.com:8080' -SkipCertificateCheck -TimeoutSec 10 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if errorlevel 1 (
    echo WARNING: Application may not be ready yet. Continuing anyway...
    echo Check app_startup.log for any startup errors.
) else (
    echo Application is ready!
)

echo.
echo ============================================
echo   RUNNING CYPRESS E2E TESTS
echo ============================================
echo.

REM Run Cypress tests
echo Running Cypress E2E tests...
yarn cy:run --browser chrome --config chromeWebSecurity=false
set "CYPRESS_EXIT_CODE=!errorlevel!"

echo.
echo Cypress tests completed with exit code: !CYPRESS_EXIT_CODE!

REM Parse the test report if jq is available
if "%JQ_AVAILABLE%"=="true" (
    echo.
    echo ============================================
    echo   PARSING TEST REPORT
    echo ============================================
    echo.
    
    call .github\scripts\parse-report.bat
    set "REPORT_EXIT_CODE=!errorlevel!"
) else (
    echo.
    echo Skipping report parsing (jq not available)
    set "REPORT_EXIT_CODE=0"
)

echo.
echo ============================================
echo   CLEANUP
echo ============================================
echo.

REM Kill any remaining application processes
echo Stopping application server...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr ":8080"') do (
    taskkill /f /pid %%a >nul 2>&1
)

REM Clean up any webpack processes
taskkill /f /im node.exe /fi "WINDOWTITLE eq webpack*" >nul 2>&1

echo Cleanup completed.

echo.
echo ============================================
echo   TEST EXECUTION SUMMARY
echo ============================================
echo.

if !CYPRESS_EXIT_CODE! equ 0 (
    echo ✅ Cypress Tests: PASSED
) else (
    echo ❌ Cypress Tests: FAILED
)

if "%JQ_AVAILABLE%"=="true" (
    if !REPORT_EXIT_CODE! equ 0 (
        echo ✅ Report Generation: SUCCESS
    ) else (
        echo ❌ Report Generation: FAILED
    )
)

echo.
echo Check the following files for details:
echo   - app_startup.log (application startup logs)
echo   - cypress\reports\cucumber-json.json (raw test results)
if "%JQ_AVAILABLE%"=="true" (
    echo   - report_output.txt (formatted test report)
)
echo   - cypress\screenshots\ (screenshots of failures)
echo   - cypress\videos\ (test execution videos)

echo.
if !CYPRESS_EXIT_CODE! equ 0 (
    echo 🎉 All tests passed successfully!
    exit /b 0
) else (
    echo ⚠️  Some tests failed. Check the logs and reports for details.
    pause
    exit /b 1
)
